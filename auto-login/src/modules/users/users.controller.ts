import {
  Controller,
  Patch,
  Param,
  Body,
  UseGuards,
  UnauthorizedException,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  Req,
  Post,
  Get,
  Query,
  Delete,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { UsersService } from './users.service';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { CreateTrialUserDto } from './dto/create-trial-user.dto';
import { User } from './entities/user.entity';

class UpdateUserStatusDto {
  status: string;
}

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Patch(':id/status')
  async updateStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserStatusDto: UpdateUserStatusDto,
    @Req() req,
  ) {
    // Check if the requesting user has admin role
    const requestingUser = req.user;
    if (requestingUser.role !== 'admin') {
      throw new UnauthorizedException(
        'Only admin users can update user status',
      );
    }

    try {
      const updatedUser = await this.usersService.updateStatus(
        id,
        updateUserStatusDto.status,
      );
      return updatedUser;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update user status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('trial')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async createTrialUser(@Body() createTrialUserDto: CreateTrialUserDto) {
    try {
      const user = await this.usersService.createTrialUser(createTrialUserDto);
      return user;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create trial user',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('trial')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findAllTrialUsers(
    @Query('page', new ParseIntPipe({ optional: true })) page = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit = 10,
  ) {
    try {
      return await this.usersService.findAllTrialUsers(page, limit);
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve trial users',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('trial/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async removeTrialUser(@Param('id', ParseIntPipe) id: number) {
    try {
      await this.usersService.removeUser(id);
      return { message: 'Trial user deleted successfully' };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to delete trial user',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('trial/:id')
  @UseGuards(RolesGuard)
  @Roles('admin')
  async findTrialUserById(@Param('id', ParseIntPipe) id: number) {
    try {
      const user = await this.usersService.findTrialUserById(id);
      return user;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to retrieve trial user',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
